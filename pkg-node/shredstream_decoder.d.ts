/* tslint:disable */
/* eslint-disable */
export function decode_entries(slot: bigint, bytes: Uint8Array): ParsedEntryWasm;
export interface ParsedEntryWasm {
    slot: number;
    entries: EntryWasm[];
}

export interface EntryWasm {
    num_hashes: number;
    hash: Hash;
    transactions: VersionedTransactionWasm[];
}

export interface MessageHeader {
    num_required_signatures: number;
    num_readonly_signed_accounts: number;
    num_readonly_unsigned_accounts: number;
}

export interface V0Message {
    header: MessageHeader;
    account_keys: Pubkey[];
    recent_blockhash: Hash;
    instructions: CompiledInstruction[];
    address_table_lookups: MessageAddressTableLookup[];
}

export interface LegacyMessage {
    header: MessageHeader;
    account_keys: Pubkey[];
    recent_blockhash: Hash;
    instructions: CompiledInstruction[];
}

export type VersionedMessage = { Legacy: LegacyMessage } | { V0: V0Message };

export interface VersionedTransactionWasm {
    signatures: Signature[];
    message: VersionedMessage;
}

export interface CompiledInstruction {
    program_id_index: number;
    accounts: number[];
    data: number[];
}

export interface MessageAddressTableLookup {
    account_key: Pubkey;
    writable_indexes: number[];
    readonly_indexes: number[];
}

export type Signature = number[];

export type Pubkey = number[];

/**
 * A hash; the 32-byte output of a hashing algorithm.
 *
 * This struct is used most often in `solana-sdk` and related crates to contain
 * a [SHA-256] hash, but may instead contain a [blake3] hash.
 *
 * [SHA-256]: https://en.wikipedia.org/wiki/SHA-2
 * [blake3]: https://github.com/BLAKE3-team/BLAKE3
 */
export class Hash {
  free(): void;
  /**
   * Create a new Hash object
   *
   * * `value` - optional hash as a base58 encoded string, `Uint8Array`, `[number]`
   */
  constructor(value: any);
  /**
   * Return the base58 string representation of the hash
   */
  toString(): string;
  /**
   * Checks if two `Hash`s are equal
   */
  equals(other: Hash): boolean;
  /**
   * Return the `Uint8Array` representation of the hash
   */
  toBytes(): Uint8Array;
}
