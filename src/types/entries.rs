use serde::{Deserialize, Serialize};
use solana_hash::Hash;
use tsify::Tsify;

use super::transactions::{VersionedTransaction, VersionedTransactionWasm};

#[derive(Serialize, Deserialize, Debug, Default, PartialEq, Eq, Clone)]
pub struct Entry {
    pub num_hashes: u64,
    pub hash: Hash,
    pub transactions: Vec<VersionedTransaction>,
}

#[derive(Serialize, Deserialize, Debug, <PERSON>lone, PartialEq, Eq)]
pub struct ParsedEntry {
    pub slot: u64,
    pub entries: Vec<Entry>,
}

#[derive(Serialize, Deserialize, Debug, Default, PartialEq, Eq, Clone, Tsify)]
#[tsify(into_wasm_abi, from_wasm_abi)]
pub struct EntryWasm {
    pub num_hashes: u64,
    pub hash: Hash,
    pub transactions: Vec<VersionedTransactionWasm>,
}

#[derive(Serialize, Deserialize, Debug, <PERSON>lone, PartialEq, Eq, Tsify)]
#[tsify(into_wasm_abi, from_wasm_abi)]
pub struct ParsedEntryWasm {
    pub slot: u64,
    pub entries: Vec<EntryWasm>,
}

impl From<Entry> for EntryWasm {
    fn from(entry: Entry) -> Self {
        Self {
            num_hashes: entry.num_hashes,
            hash: entry.hash,
            transactions: entry.transactions.into_iter().map(|tx| tx.into()).collect(),
        }
    }
}

impl From<ParsedEntry> for ParsedEntryWasm {
    fn from(parsed: ParsedEntry) -> Self {
        Self { slot: parsed.slot, entries: parsed.entries.into_iter().map(|entry| entry.into()).collect() }
    }
}
