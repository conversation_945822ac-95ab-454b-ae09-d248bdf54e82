use serde::{Deserialize, Serialize};
use solana_short_vec as short_vec;
use tsify::Tsify;

use super::accounts::Signature;
use super::messages::VersionedMessage;

#[derive(Serialize, Deserialize, Debug, PartialEq, De<PERSON>ult, <PERSON>q, <PERSON>lone)]
pub struct VersionedTransaction {
    #[serde(with = "short_vec")]
    pub signatures: Vec<Signature>,
    pub message: VersionedMessage,
}

#[derive(Serialize, Deserialize, Debug, PartialEq, Default, Eq, Clone, Tsify)]
#[tsify(into_wasm_abi, from_wasm_abi)]
pub struct VersionedTransactionWasm {
    pub signatures: Vec<Signature>,
    pub message: VersionedMessage,
}

impl From<VersionedTransaction> for VersionedTransactionWasm {
    fn from(tx: VersionedTransaction) -> Self {
        Self { signatures: tx.signatures, message: tx.message }
    }
}

impl From<VersionedTransactionWasm> for VersionedTransaction {
    fn from(tx: VersionedTransactionWasm) -> Self {
        Self { signatures: tx.signatures, message: tx.message }
    }
}
