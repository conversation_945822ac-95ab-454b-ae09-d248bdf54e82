import { readFileSync } from 'fs'
import { decode_entries } from '../pkg-node/shredstream_decoder.js'

async function testDecodeEntries() {
    const shred = readFileSync('../tests/data/shred_000001.bin')

    const { entries } = decode_entries(1n, shred)

    for (const entry of entries) {
        console.log(
            `Entry: ${entry.num_hashes} hashes, hash: ${entry.hash}, transactions: ${entry.transactions.length}`
        )

        for (const tx of entry.transactions) {
            console.log('tx.signatures type:', typeof tx.signatures)
            console.log('tx.signatures length:', tx.signatures.length)
            console.log('tx.signatures:', tx.signatures)

            for (let i = 0; i < tx.signatures.length; i++) {
                const sig = tx.signatures[i]
                console.log(`  signature[${i}] type:`, typeof sig)
                console.log(`  signature[${i}] length:`, sig.length)
                console.log(`  signature[${i}]:`, sig)
            }
        }
    }
}

testDecodeEntries()
