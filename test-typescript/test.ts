import { readFileSync } from 'fs'
import { decode_entries } from '../pkg-node/shredstream_decoder.js'

async function testDecodeEntries() {
    const shred = readFileSync('../tests/data/shred_000001.bin')

    const { entries } = decode_entries(1n, shred)

    for (const entry of entries) {
        console.log(
            `Entry: ${entry.num_hashes} hashes, hash: ${entry.hash}, transactions: ${entry.transactions.length}`
        )

        for (const tx of entry.transactions) {
            console.log(tx.signatures)
        }
    }
}

testDecodeEntries()
