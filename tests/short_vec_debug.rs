use shredstream_decoder::types::{LegacyMessage, Signature, VersionedMessage, VersionedTransaction};
use solana_short_vec as short_vec;

#[test]
fn test_short_vec_signature_serialization() {
    let sig1 = Signature::new_from_array([1u8; 64]);
    let sig2 = Signature::new_from_array([2u8; 64]);

    let transaction = VersionedTransaction {
        signatures: vec![sig1, sig2],
        message: VersionedMessage::Legacy(LegacyMessage::default()),
    };

    let serialized = bincode::serialize(&transaction).unwrap();
    println!("Serialized transaction bytes: {:?}", serialized);
    println!("Serialized transaction length: {}", serialized.len());

    let deserialized: VersionedTransaction = bincode::deserialize(&serialized).unwrap();
    println!("Deserialized signatures count: {}", deserialized.signatures.len());

    for (i, sig) in deserialized.signatures.iter().enumerate() {
        println!("Signature {}: {:?}", i, sig.as_ref());
    }

    assert_eq!(transaction, deserialized);
}

#[test]
fn test_single_signature_serialization() {
    let sig = Signature::new_from_array([42u8; 64]);

    let transaction =
        VersionedTransaction { signatures: vec![sig], message: VersionedMessage::Legacy(LegacyMessage::default()) };

    let serialized = bincode::serialize(&transaction).unwrap();
    println!("Single sig serialized bytes: {:?}", serialized);
    println!("Single sig serialized length: {}", serialized.len());

    let deserialized: VersionedTransaction = bincode::deserialize(&serialized).unwrap();
    println!("Single sig deserialized count: {}", deserialized.signatures.len());

    assert_eq!(deserialized.signatures.len(), 1);
    assert_eq!(deserialized.signatures[0].as_ref(), &[42u8; 64]);
}

#[test]
fn test_short_vec_encoding_manually() {
    let sig = Signature::new_from_array([123u8; 64]);
    let signatures = vec![sig];

    // Test serialization with serde and short_vec
    #[derive(serde::Serialize, serde::Deserialize)]
    struct TestStruct {
        #[serde(with = "short_vec")]
        signatures: Vec<Signature>,
    }

    let test_data = TestStruct { signatures };
    let encoded = bincode::serialize(&test_data).unwrap();

    println!("Manual short_vec encoded: {:?}", encoded);
    println!("Manual short_vec length: {}", encoded.len());

    let decoded: TestStruct = bincode::deserialize(&encoded).unwrap();
    println!("Manual decoded count: {}", decoded.signatures.len());
    assert_eq!(decoded.signatures.len(), 1);
    assert_eq!(decoded.signatures[0].as_ref(), &[123u8; 64]);
}

#[test]
fn test_signature_raw_serialization() {
    let sig = Signature::new_from_array([99u8; 64]);

    let serialized = bincode::serialize(&sig).unwrap();
    println!("Raw signature serialized: {:?}", serialized);
    println!("Raw signature length: {}", serialized.len());

    let deserialized: Signature = bincode::deserialize(&serialized).unwrap();
    assert_eq!(sig, deserialized);
    assert_eq!(deserialized.as_ref(), &[99u8; 64]);
}
