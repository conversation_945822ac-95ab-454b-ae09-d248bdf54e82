mod common;

use common::compatibility_validators::validators::{
    validate_signature_binary_compatibility, validate_signature_compatibility, CompatibilityResult,
};
use shredstream_decoder::types::Signature as CustomSignature;
use solana_signature::Signature as SolanaSignature;

mod signature_compatibility_tests {
    use super::*;

    #[test]
    fn test_signature_binary_compatibility_basic() {
        let test_bytes = [42u8; 64];

        let custom_sig = CustomSignature::new_from_array(test_bytes);
        let solana_sig = SolanaSignature::from(test_bytes);

        assert!(validate_signature_compatibility(&custom_sig, &solana_sig));

        let result = validate_signature_binary_compatibility(&custom_sig, &solana_sig);
        assert_eq!(result, CompatibilityResult::Success);
    }

    #[test]
    fn test_signature_binary_compatibility_zero_bytes() {
        let test_bytes = [0u8; 64];

        let custom_sig = CustomSignature::new_from_array(test_bytes);
        let solana_sig = SolanaSignature::from(test_bytes);

        assert!(validate_signature_compatibility(&custom_sig, &solana_sig));

        let result = validate_signature_binary_compatibility(&custom_sig, &solana_sig);
        assert_eq!(result, CompatibilityResult::Success);
    }

    #[test]
    fn test_signature_binary_compatibility_max_bytes() {
        let test_bytes = [255u8; 64];

        let custom_sig = CustomSignature::new_from_array(test_bytes);
        let solana_sig = SolanaSignature::from(test_bytes);

        assert!(validate_signature_compatibility(&custom_sig, &solana_sig));

        let result = validate_signature_binary_compatibility(&custom_sig, &solana_sig);
        assert_eq!(result, CompatibilityResult::Success);
    }

    #[test]
    fn test_signature_binary_compatibility_random_patterns() {
        let test_patterns = vec![
            [1u8; 64],
            [123u8; 64],
            {
                let mut bytes = [0u8; 64];
                for i in 0..64 {
                    bytes[i] = i as u8;
                }
                bytes
            },
            {
                let mut bytes = [0u8; 64];
                for i in 0..64 {
                    bytes[i] = (i * 17 + 31) as u8;
                }
                bytes
            },
        ];

        for (i, test_bytes) in test_patterns.iter().enumerate() {
            let custom_sig = CustomSignature::new_from_array(*test_bytes);
            let solana_sig = SolanaSignature::from(*test_bytes);

            assert!(
                validate_signature_compatibility(&custom_sig, &solana_sig),
                "Pattern {} failed compatibility check",
                i
            );

            let result = validate_signature_binary_compatibility(&custom_sig, &solana_sig);
            assert_eq!(result, CompatibilityResult::Success, "Pattern {} failed binary compatibility", i);
        }
    }

    #[test]
    fn test_signature_serialization_roundtrip() {
        let test_bytes = [42u8; 64];

        let custom_sig = CustomSignature::new_from_array(test_bytes);
        let solana_sig = SolanaSignature::from(test_bytes);

        let custom_serialized = bincode::serialize(&custom_sig).unwrap();
        let solana_serialized = bincode::serialize(&solana_sig).unwrap();

        assert_eq!(custom_serialized, solana_serialized, "Serialized bytes must be identical");

        let custom_deserialized: CustomSignature = bincode::deserialize(&custom_serialized).unwrap();
        let solana_deserialized: SolanaSignature = bincode::deserialize(&solana_serialized).unwrap();

        assert_eq!(custom_sig.as_ref(), custom_deserialized.as_ref());
        assert_eq!(solana_sig.as_ref(), solana_deserialized.as_ref());
        assert_eq!(custom_deserialized.as_ref(), solana_deserialized.as_ref());
    }

    #[test]
    fn test_signature_cross_deserialization() {
        let test_bytes = [123u8; 64];

        let custom_sig = CustomSignature::new_from_array(test_bytes);
        let solana_sig = SolanaSignature::from(test_bytes);

        let custom_serialized = bincode::serialize(&custom_sig).unwrap();
        let solana_serialized = bincode::serialize(&solana_sig).unwrap();

        let custom_from_solana: CustomSignature = bincode::deserialize(&solana_serialized).unwrap();
        let solana_from_custom: SolanaSignature = bincode::deserialize(&custom_serialized).unwrap();

        assert_eq!(custom_sig.as_ref(), custom_from_solana.as_ref());
        assert_eq!(solana_sig.as_ref(), solana_from_custom.as_ref());
        assert_eq!(custom_from_solana.as_ref(), solana_from_custom.as_ref());
    }

    #[test]
    fn test_signature_conversion_compatibility() {
        let test_bytes = [200u8; 64];

        let custom_sig = CustomSignature::new_from_array(test_bytes);
        let solana_sig = SolanaSignature::from(test_bytes);

        let custom_to_solana: SolanaSignature = custom_sig.into();
        let solana_to_custom: CustomSignature = solana_sig.into();

        assert_eq!(custom_sig.as_ref(), custom_to_solana.as_ref());
        assert_eq!(solana_sig.as_ref(), solana_to_custom.as_ref());
        assert_eq!(custom_to_solana.as_ref(), solana_to_custom.as_ref());
    }

    #[test]
    fn test_signature_default_compatibility() {
        let custom_default = CustomSignature::default();
        let solana_default = SolanaSignature::default();

        assert_eq!(custom_default.as_ref(), solana_default.as_ref());

        let result = validate_signature_binary_compatibility(&custom_default, &solana_default);
        assert_eq!(result, CompatibilityResult::Success);
    }

    #[test]
    fn test_signature_as_ref_compatibility() {
        let test_bytes = [77u8; 64];

        let custom_sig = CustomSignature::new_from_array(test_bytes);
        let solana_sig = SolanaSignature::from(test_bytes);

        assert_eq!(custom_sig.as_ref(), &test_bytes[..]);
        assert_eq!(solana_sig.as_ref(), &test_bytes[..]);
        assert_eq!(custom_sig.as_ref(), solana_sig.as_ref());
    }

    #[test]
    fn test_signature_to_bytes_compatibility() {
        let test_bytes = [88u8; 64];

        let custom_sig = CustomSignature::new_from_array(test_bytes);

        assert_eq!(custom_sig.to_bytes(), test_bytes);
        assert_eq!(custom_sig.as_ref(), &test_bytes[..]);
    }

    #[test]
    fn test_signature_comprehensive_compatibility() {
        let test_cases = vec![[0u8; 64], [255u8; 64], [42u8; 64], {
            let mut bytes = [0u8; 64];
            for i in 0..64 {
                bytes[i] = (i % 256) as u8;
            }
            bytes
        }];

        for (i, test_bytes) in test_cases.iter().enumerate() {
            let custom_sig = CustomSignature::new_from_array(*test_bytes);
            let solana_sig = SolanaSignature::from(*test_bytes);

            assert!(
                validate_signature_compatibility(&custom_sig, &solana_sig),
                "Test case {} failed basic compatibility",
                i
            );

            let result = validate_signature_binary_compatibility(&custom_sig, &solana_sig);
            assert_eq!(result, CompatibilityResult::Success, "Test case {} failed binary compatibility", i);

            let custom_serialized = bincode::serialize(&custom_sig).unwrap();
            let solana_serialized = bincode::serialize(&solana_sig).unwrap();
            assert_eq!(custom_serialized, solana_serialized, "Test case {} serialization mismatch", i);

            let custom_deserialized: CustomSignature = bincode::deserialize(&solana_serialized).unwrap();
            let solana_deserialized: SolanaSignature = bincode::deserialize(&custom_serialized).unwrap();
            assert_eq!(
                custom_deserialized.as_ref(),
                solana_deserialized.as_ref(),
                "Test case {} cross-deserialization mismatch",
                i
            );
        }
    }
}
